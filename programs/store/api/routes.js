import {apiHandlerFactory} from './helpers';

// Common.
import * as common from './common';

// Customer.
import * as customers from './customers';

// Cart.
import * as cart from './cart';

// Cart.
import * as checkout from './checkout';

// Catalog.
import * as catalog from './catalog';

// PCM
import * as pcm from './pcm';

export default function (app) {
    // Common.
    app.post('/api/store/common/cities', apiHandlerFactory(app, common.cities));
    app.post('/api/store/common/countries', apiHandlerFactory(app, common.countries));
    app.post('/api/store/common/districts', apiHandlerFactory(app, common.districts));
    app.post('/api/store/common/sub-districts', apiHandlerFactory(app, common.subDistricts));
    app.get('/api/store/common/image/:id', (request, response) => common.image(app, request, response));
    app.post('/api/store/common/info', apiHandlerFactory(app, common.info));
    app.post('/api/store/common/page', apiHandlerFactory(app, common.page));
    app.post('/api/store/common/states', apiHandlerFactory(app, common.states));
    app.post('/api/store/common/sitemap-records', apiHandlerFactory(app, common.sitemapRecords));
    app.post('/api/store/common/text', apiHandlerFactory(app, common.text));
    app.post('/api/store/common/analytics', apiHandlerFactory(app, common.analytics));
    app.post('/api/store/common/branches', apiHandlerFactory(app, common.branches));

    // Customer.
    app.post('/api/store/customers/add-to-collection', apiHandlerFactory(app, customers.addToCollection));
    app.post('/api/store/customers/add-to-favorites', apiHandlerFactory(app, customers.addToFavorites));
    app.post('/api/store/customers/all-orders', apiHandlerFactory(app, customers.allOrders));
    app.post(
        '/api/store/customers/cancel-order-cancellation-request',
        apiHandlerFactory(app, customers.cancelOrderCancellationRequest)
    );
    app.post('/api/store/customers/cancel-return-order', apiHandlerFactory(app, customers.cancelReturnOrder));
    app.post('/api/store/customers/change-password', apiHandlerFactory(app, customers.changePassword));
    app.post('/api/store/customers/check-review-create', apiHandlerFactory(app, customers.checkReviewCreate));
    app.post('/api/store/customers/collections', apiHandlerFactory(app, customers.collections));
    app.post('/api/store/customers/collection-products', apiHandlerFactory(app, customers.collectionProducts));
    app.post('/api/store/customers/contacts', apiHandlerFactory(app, customers.contacts));
    app.post('/api/store/customers/create-customer', apiHandlerFactory(app, customers.createCustomer));
    app.post('/api/store/customers/create-return-order', apiHandlerFactory(app, customers.createReturnOrder));
    app.post('/api/store/customers/create-review', apiHandlerFactory(app, customers.createReview));
    app.post('/api/store/customers/customer', apiHandlerFactory(app, customers.customer));
    app.post('/api/store/customers/customers', apiHandlerFactory(app, customers.customers));
    app.post('/api/store/customers/get-customers', apiHandlerFactory(app, customers.getCustomers));
    app.post('/api/store/customers/download-invoice', apiHandlerFactory(app, customers.downloadInvoice));
    app.post('/api/store/customers/favorite-products', apiHandlerFactory(app, customers.favoriteProducts));
    app.post('/api/store/customers/invoices', apiHandlerFactory(app, customers.invoices));
    app.post('/api/store/customers/ledger', apiHandlerFactory(app, customers.ledger));
    app.post('/api/store/customers/make-payment', apiHandlerFactory(app, customers.makePayment));
    app.post('/api/store/customers/order', apiHandlerFactory(app, customers.order));
    app.post('/api/store/customers/orders', apiHandlerFactory(app, customers.orders));
    app.post(
        '/api/store/customers/product-params-for-customer',
        apiHandlerFactory(app, customers.productParamsForCustomer)
    );
    app.post('/api/store/customers/receipts', apiHandlerFactory(app, customers.receipts));
    app.post('/api/store/customers/remove-collection', apiHandlerFactory(app, customers.removeCollection));
    app.post('/api/store/customers/remove-contact', apiHandlerFactory(app, customers.removeContact));
    app.post('/api/store/customers/remove-from-collection', apiHandlerFactory(app, customers.removeFromCollection));
    app.post('/api/store/customers/remove-from-favorites', apiHandlerFactory(app, customers.removeFromFavorites));
    app.post(
        '/api/store/customers/request-order-cancellation',
        apiHandlerFactory(app, customers.requestOrderCancellation)
    );
    app.post('/api/store/customers/reset-password', apiHandlerFactory(app, customers.resetPassword));
    app.post('/api/store/customers/reviews', apiHandlerFactory(app, customers.reviews));
    app.post('/api/store/customers/save-collection', apiHandlerFactory(app, customers.saveCollection));
    app.post('/api/store/customers/save-contact', apiHandlerFactory(app, customers.saveContact));
    app.post('/api/store/customers/save-product-collections', apiHandlerFactory(app, customers.saveProductCollections));
    app.post('/api/store/customers/send-reset-password-link', apiHandlerFactory(app, customers.sendResetPasswordLink));
    app.post('/api/store/customers/update-customer', apiHandlerFactory(app, customers.updateCustomer));
    app.post('/api/store/customers/delete-customer', apiHandlerFactory(app, customers.deleteCustomer));
    app.post(
        '/api/store/customers/sales-representative-details',
        apiHandlerFactory(app, customers.saleRepresentativeDetails)
    );

    app.post('/api/store/customers/exchange-rates', apiHandlerFactory(app, customers.exchangeRates));
    app.post('/api/store/customers/subscribers', apiHandlerFactory(app, customers.subscribers));

    // Cart.
    app.post('/api/store/cart', apiHandlerFactory(app, cart.cart));
    app.post('/api/store/cart/apply-coupon-code', apiHandlerFactory(app, cart.applyCouponCode));
    app.post('/api/store/cart/add-item', apiHandlerFactory(app, cart.addItem));
    app.post('/api/store/cart/set-customer', apiHandlerFactory(app, cart.setCustomer));
    app.post('/api/store/cart/update-item', apiHandlerFactory(app, cart.updateItem));
    app.post('/api/store/cart/remove-item', apiHandlerFactory(app, cart.removeItem));
    app.post('/api/store/cart/remove-items', apiHandlerFactory(app, cart.removeItems));
    app.post('/api/store/cart/campaign-products', apiHandlerFactory(app, cart.campaignProducts));
    app.post('/api/store/cart/update-cart', apiHandlerFactory(app, cart.updateCart));

    // Checkout.
    app.post('/api/store/checkout/approve-payment', apiHandlerFactory(app, checkout.approvePayment));
    app.post('/api/store/checkout/change-billing-address', apiHandlerFactory(app, checkout.changeBillingAddress));
    app.post('/api/store/checkout/change-delivery-address', apiHandlerFactory(app, checkout.changeDeliveryAddress));
    app.post('/api/store/checkout/create-payment-intent', apiHandlerFactory(app, checkout.createPaymentIntent));
    app.post(
        '/api/store/checkout/create-stripe-payment-intent',
        apiHandlerFactory(app, checkout.createStripePaymentIntent)
    );
    app.post('/api/store/checkout/delivery-options', apiHandlerFactory(app, checkout.deliveryOptions));
    app.post('/api/store/checkout/get-card-details', apiHandlerFactory(app, checkout.getCardDetails));
    app.post('/api/store/checkout/get-fields-for-texts', apiHandlerFactory(app, checkout.getFieldsForTexts));
    app.post('/api/store/checkout/get-payment-intent', apiHandlerFactory(app, checkout.getPaymentIntent));
    app.post('/api/store/checkout/installments', apiHandlerFactory(app, checkout.installments));
    app.post('/api/store/checkout/payment-methods', apiHandlerFactory(app, checkout.paymentMethods));
    app.post('/api/store/checkout/save-delivery', apiHandlerFactory(app, checkout.saveDelivery));
    app.post('/api/store/checkout/save-information', apiHandlerFactory(app, checkout.saveInformation));
    app.post('/api/store/checkout/update-delivery-type', apiHandlerFactory(app, checkout.updateDeliveryType));
    app.post('/api/store/checkout/update-installment-count', apiHandlerFactory(app, checkout.updateInstallmentCount));
    app.post('/api/store/checkout/update-payment-intent', apiHandlerFactory(app, checkout.updatePaymentIntent));
    app.post('/api/store/checkout/update-payment-method-id', apiHandlerFactory(app, checkout.updatePaymentMethodId));
    app.post('/api/store/checkout/update-step', apiHandlerFactory(app, checkout.updateStep));
    app.post(
        '/api/store/checkout/update-sub-payment-method-id',
        apiHandlerFactory(app, checkout.updateSubPaymentMethodId)
    );
    app.post('/api/store/checkout/store-delivery-warehouses', apiHandlerFactory(app, checkout.storeDeliveryWarehouses));

    // Catalog.
    app.post('/api/store/catalog/brand', apiHandlerFactory(app, catalog.brand));
    app.post('/api/store/catalog/brands', apiHandlerFactory(app, catalog.brands));
    app.post('/api/store/catalog/filters', apiHandlerFactory(app, catalog.filters));
    app.post('/api/store/catalog/product', apiHandlerFactory(app, catalog.product));
    app.post('/api/store/catalog/product-reviews', apiHandlerFactory(app, catalog.productReviews));
    app.post('/api/store/catalog/products', apiHandlerFactory(app, catalog.products));
    app.post('/api/store/catalog/search', apiHandlerFactory(app, catalog.search));
    app.post('/api/store/catalog/save-popular-search', apiHandlerFactory(app, catalog.savePopularSearch));
    app.post('/api/store/catalog/product-stocks', apiHandlerFactory(app, catalog.productStocks));

    // PCM.
    app.post('/api/store/pcm/models', apiHandlerFactory(app, pcm.models));
    app.post('/api/store/pcm/get-payload', apiHandlerFactory(app, pcm.getPayload));
    app.post('/api/store/pcm/save-configuration', apiHandlerFactory(app, pcm.saveConfiguration));
    app.post('/api/store/pcm/get-configuration', apiHandlerFactory(app, pcm.getConfiguration));
}
