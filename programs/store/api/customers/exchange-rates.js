export default async function (app, store, request, response) {
    const {currencyId, date} = request.body || {};

    let targetDate;
    if (date) {
        targetDate = app.datetime.fromISO(date);
        if (!targetDate.isValid) {
            return response.status(400).json({code: 400, message: 'Invalid DateTime: invalid input'});
        }
    } else {
        targetDate = app.datetime.local();
    }

    const startDate = targetDate.startOf('day').toJSDate();
    const endDate = targetDate.endOf('day').toJSDate();

    try {
        const company = await app.collection('kernel.company').findOne({});
        const currencies = await app.collection('kernel.currencies').find({_id: {$ne: company.currency._id}});
        const currencyIds = currencies.map(currency => currency._id);
        if (currencyId) {
            if (!currencyIds.includes(currencyId)) {
                return response.status(400).json({code: 400, message: 'Invalid currencyId'});
            }
        }
        const rates = await app.collection('kernel.exchange-rates').find({
            currencyId: currencyId ? currencyId : {$in: currencyIds},
            date: {
                $gte: startDate,
                $lte: endDate
            }
        });

        return response.status(200).json({
            status: 'success',
            data: rates
        });
    } catch (error) {
        return response.status(500).json({status: 'error', message: error.message});
    }
}
