import _ from 'lodash';
import axios from 'axios';
import {trim} from 'framework/helpers';
import {findInstallment} from '../checkout/utils';
import {integrations} from '../../../finance/methods/online-pos-receipts';

export default async function (app, store, request, response) {
    const {
        customerId,
        amount,
        amountUSD,
        exchangeRate,
        installmentCount,
        cardBrand,
        cardNumber,
        cardHolder,
        expireYear,
        expireMonth,
        cvv,
        selectedCardBrand
    } = request.body;

    // Get real card brand.
    if (typeof cardNumber !== 'string' || cardNumber.length < 16) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Invalid card number'
        });
    }
    
    const cardInfoResponse = await axios({
        method: 'post',
        url: 'https://api.entererp.com/v1/common/bin-detail',
        headers: {
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({cardNumber})
    });
    
    const realCardBrand = cardInfoResponse.data.cardBrandCode;
    if (cardBrand !== realCardBrand) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Provided card brand is not the same as real card brand!'
        });
    }

    // Dual currency logic - determine which amount to use for installment calculation
    let finalAmount = amount;
    let isUsingForeignCurrency = false;
    let currencyId = store.currencyId;
    let currencyRate = 1;
    
    // Check if we have both TL and USD amounts
    if (amount && amountUSD && exchangeRate) {
        // If store currency is not TRY and we have USD amount, use USD
        if (store.currencyId !== 'TRY' && amountUSD > 0) {
            finalAmount = amountUSD;
            currencyId = 'USD';
            currencyRate = exchangeRate;
            isUsingForeignCurrency = true;
        } else {
            // Use TL amount
            finalAmount = amount;
            currencyId = store.currencyId;
            currencyRate = 1;
        }
    } else if (amountUSD && !amount) {
        // Only USD amount provided
        finalAmount = amountUSD;
        currencyId = 'USD';
        currencyRate = exchangeRate || 1;
        isUsingForeignCurrency = true;
    }

    // Find installment with the final amount
    const installment = await findInstallment(
        app,
        store,
        cardBrand,
        installmentCount,
        finalAmount,
        selectedCardBrand,
        customerId
    );

    // Get pos.
    const pos = await app.collection('accounting.pos').findOne({
        _id: installment.posId,
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    if (!pos || !pos.integrationType) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'POS not found!'
        });
    }

    // Prepare financial entry payload with dual currency support.
    const bankAccount = await app.collection('accounting.bank-accounts').findOne({
        _id: pos.bankAccountId,
        $disableBranchCheck: true
    });

    if (!bankAccount) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Bank account not found for POS!'
        });
    }

    const paymentJournal = await app.collection('accounting.journals').findOne({
        _id: bankAccount.journalId,
        $disableBranchCheck: true,
        $select: ['_id']
    });

    if (!paymentJournal) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Payment journal not found!'
        });
    }

    const posJournal = await app.collection('accounting.journals').findOne({
        _id: pos.journalId,
        $disableBranchCheck: true,
        $select: ['shortDescription']
    });

    if (!posJournal) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'POS journal not found!'
        });
    }
    
    const now = app.datetime.local().startOf('day');
    const entry = {};
    entry.status = 'draft';
    entry.type = 'receipt';
    entry.documentType = 'pos';
    entry.journalId = pos.journalId;
    entry.paymentAccountId = paymentJournal._id;
    entry.posId = pos._id;
    entry.partnerType = 'customer';
    entry.partnerId = customerId;
    
    // Enhanced entry with dual currency support
    entry.amount = amount || finalAmount; // TL amount
    entry.amountUSD = amountUSD; // USD amount
    entry.baseTotal = amount || finalAmount;
    entry.dueDifference = installment.total - finalAmount;
    entry.total = installment.total;
    entry.installmentCount = installmentCount;
    
    if (_.isFinite(installment.plusInstallmentCount)) {
        entry.plusInstallmentCount = installment.plusInstallmentCount;
    }
    
    entry.installmentAmount = installment.installmentAmount;
    entry.branchId = store.branchId;
    entry.currencyId = currencyId;
    entry.currencyRate = currencyRate;
    entry.exchangeRateUsed = exchangeRate;
    entry.isUsingForeignCurrency = isUsingForeignCurrency;
    entry.reference = '';
    entry.description = posJournal.shortDescription || app.translate('EnterStore online POS receipt.');
    entry.recordDate = app.datetime.local().toJSDate();
    entry.issueDate = now.toJSDate();
    entry.dueDate = now.toJSDate();
    
    // Due date calculation (existing logic)
    if (pos.paymentOnSpecificDate) {
        const cutoffDate = pos.cutoffDate;

        if (now.day > cutoffDate) {
            entry.dueDate = now.plus({months: 1}).set({day: cutoffDate}).toJSDate();
        } else {
            entry.dueDate = now.set({day: cutoffDate}).toJSDate();
        }

        entry.dueDate = app.datetime
            .fromJSDate(entry.dueDate)
            .plus({months: entry.installmentCount - 1})
            .toJSDate();
    } else {
        if (pos.posRefund === 'lump-sum-payment') {
            const lumpSumPayment = pos.lumpSumPayments.find(c => c.installment === entry.installmentCount);

            if (!!lumpSumPayment) {
                entry.dueDate = now.plus({days: lumpSumPayment.refund || 0}).toJSDate();
            }
        } else {
            const installmentPayment = pos.installmentPayments.find(c => c.installment === entry.installmentCount);

            if (!!installmentPayment) {
                entry.dueDate = now.plus({days: installmentPayment.refund || 0}).toJSDate();
            }
        }
    }

    // Get user.
    const user = await app.collection('kernel.users').findOne({isRoot: true});

    if (!user) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Root user not found!'
        });
    }

    // Get currency.
    const currency = await app.collection('kernel.currencies').findOne({
        _id: currencyId,
        $select: ['name'],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });

    if (!currency) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Currency not found!'
        });
    }

    // Prepare partner.
    const customer = await app.collection('kernel.partners').findOne({
        _id: customerId,
        $select: ['name', 'email', 'phone', 'address'],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });

    if (!customer) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Customer not found!'
        });
    }
    
    const partner = {
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: customer.address
    };

    // Get result with dual currency data
    const url = store.website || '';
    const data = {
        cardBrand,
        cardNumber,
        cardHolder,
        expireMonth,
        expireYear,
        cvv,
        amount: installment.total,
        installmentCount,
        posId: pos._id,
        partnerId: customerId,
        currencyId: currency._id,
        currencyRate: currencyRate,
        description: posJournal.shortDescription || app.translate('EnterStore online POS receipt.'),
        pos,
        partner,
        currency,
        storeId: store._id,
        branchId: store.branchId,
        
        // Enhanced data with dual currency support
        tutar: amount, // TL amount
        tutar_sbp: amountUSD, // USD amount (SBP)
        exchangeRateUsed: exchangeRate,
        isUsingForeignCurrency: isUsingForeignCurrency,
        evaluateAccountCurrency: isUsingForeignCurrency,
        globalCurrencyRate: 1,
        
        ...(pos.integrationType === 'paytr'
            ? {
                  okUrl: `${trim(url, '/')}/api/checkout/paytr-success`,
                  failUrl: `${trim(url, '/')}/api/checkout/paytr-error`,
                  frameUrl: `${trim(url, '/')}/api/checkout/paytr-frame-3d`
              }
            : {}),
        entryPayload: entry
    };

    const result = await integrations[pos.integrationType].charge(app, data, {user});

    return response.json({
        customerName: partner.name,
        cardBrandCode: installment.cardBrandCode,
        cardBrandName: installment.cardBrandName,
        cardBrandLogo: installment.cardBrandLogo,
        installmentCount: installment.installmentCount,
        plusInstallmentCount: installment.plusInstallmentCount,
        installmentRate: installment.installmentRate,
        installmentAmount: installment.installmentAmount,
        total: installment.total,
        
        // Enhanced response with dual currency info
        amountTL: amount,
        amountUSD: amountUSD,
        exchangeRate: exchangeRate,
        currencyUsed: currencyId,
        isUsingForeignCurrency: isUsingForeignCurrency,
        
        ...result
    });
}
