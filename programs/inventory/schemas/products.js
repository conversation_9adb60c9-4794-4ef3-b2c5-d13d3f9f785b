import _ from 'lodash';

export default {
    name: 'products',
    title: 'Products',
    collection: 'inventory.products',
    workflow: {
        allowedOperations: ['create', 'update', 'remove'],
        allowedActions: ['notification', 'validation'],
        documentView: 'inventory.catalog.products-detail'
    },
    async fields(app) {
        const fields = [
            {type: 'string', name: 'code', label: 'Code'},
            {type: 'string', name: 'name', label: 'Name'},
            {type: 'string', name: 'definition', label: 'Definition'},
            {type: 'string', name: 'type', label: 'Type'},
            {type: 'string', name: 'typeValue', label: 'Type value'},
            {type: 'text', name: 'categoryPath', label: 'Category'},
            {type: 'text', name: 'groups', label: 'Groups'},
            {type: 'string', name: 'barcode', label: 'Barcode'},
            {type: 'string', name: 'baseUnit', label: 'Base unit'},
            {type: 'text', name: 'description', label: 'Short description'},
            {type: 'string', name: 'brand', label: 'Brand'},
            {type: 'boolean', name: 'canBeSold', label: 'Can be sold'},
            {type: 'boolean', name: 'canBePurchased', label: 'Can be purchased'},
            {type: 'boolean', name: 'canBeExpensed', label: 'Can be expensed'},
            {type: 'boolean', name: 'isConsumable', label: 'Is consumable'},
            {type: 'boolean', name: 'isFixture', label: 'Is fixture'},
            {type: 'boolean', name: 'isServiceProduct', label: 'Is service product'},
            {type: 'boolean', name: 'isECommerceProduct', label: 'Is e-commerce product'},
            {type: 'boolean', name: 'isSimple', label: 'Is simple'},
            {type: 'boolean', name: 'isVariant', label: 'Is variant'},
            {type: 'boolean', name: 'isConfigurable', label: 'Is configurable'},
            {type: 'boolean', name: 'isKit', label: 'Is kit product'},
            {type: 'boolean', name: 'useSubProducts', label: 'Use sub products'},
            {type: 'boolean', name: 'hasAlternate', label: 'Has alternates'},
            {type: 'boolean', name: 'isActive', label: 'Is active'},
            {type: 'string', name: 'typeOfGoods', label: 'Type of goods'},
            {type: 'string', name: 'hsCode', label: 'HS code'},
            {type: 'string', name: 'manufacturer', label: 'Manufacturer'},
            {type: 'string', name: 'content', label: 'Content'},
            {type: 'string', name: 'manufacturerProductCode', label: 'Manufacturer product code'},
            {type: 'string', name: 'countryOfManufacture', label: 'Country of manufacture'},
            {type: 'string', name: 'classificationCode', label: 'Classification code'},
            {type: 'string', name: 'classificationVersion', label: 'Classification version'},
            {type: 'string', name: 'classificationValue', label: 'Classification value'},
            {type: 'string', name: 'countryOfOrigin', label: 'Country of origin'},
            {type: 'string', name: 'containerType', label: 'Container type'},
            {type: 'string', name: 'containerNo', label: 'Container no'},
            {type: 'string', name: 'containerBrand', label: 'Container brand'},
            {type: 'string', name: 'shippingUnit', label: 'Shipping unit'},
            {type: 'decimal', name: 'salesPrice', label: 'Sales price'},
            {type: 'string', name: 'salesUnit', label: 'Sales unit'},
            {type: 'string', name: 'salesTax', label: 'Sales tax'},
            {type: 'string', name: 'salesAdditionalTaxes', label: 'Sales additional taxes'},
            {type: 'text', name: 'salesNote', label: 'Sales note'},
            {type: 'boolean', name: 'priceIsDeterminedBySubProducts', label: 'Price is determined by sub-products'},
            {type: 'text', name: 'ecommerceSeoTitle', label: 'E-commerce SEO title'},
            {type: 'text', name: 'ecommerceSeoDescription', label: 'E-commerce SEO description'},
            {type: 'text', name: 'ecommerceDeliveryOptions', label: 'E-commerce delivery options'},
            {type: 'boolean', name: 'ecommerceDropshipping', label: 'E-commerce dropshipping'},
            {type: 'boolean', name: 'ecommerceSameDayDelivery', label: 'E-commerce same day delivery'},
            {
                type: 'decimal',
                name: 'ecommerceEstimatedDeliveryDuration',
                label: 'E-commerce estimated delivery duration'
            },
            {
                type: 'boolean',
                name: 'ecommerceDeliveryAtSpecifiedDate',
                label: 'E-commerce delivery at a specified date'
            },
            {
                type: 'boolean',
                name: 'ecommerceDeliveryAtSpecifiedTime',
                label: 'E-commerce delivery at a specified time'
            },
            {type: 'decimal', name: 'ecommerceDailyStorageCost', label: 'E-commerce daily storage cost'},
            {type: 'string', name: 'purchaseUnit', label: 'Purchase unit'},
            {type: 'string', name: 'purchaseTax', label: 'Purchase tax'},
            {type: 'string', name: 'purchaseAdditionalTaxes', label: 'Purchase additional taxes'},
            {type: 'text', name: 'purchaseNote', label: 'Purchase note'},
            {type: 'decimal', name: 'lastPurchasePrice', label: 'Last purchase price'},
            {type: 'string', name: 'stockUnit', label: 'Stock unit'},
            {type: 'string', name: 'tracking', label: 'Tracing'},
            {type: 'string', name: 'trackingValue', label: 'Tracing value'},
            {type: 'string', name: 'putawayStrategy', label: 'Putaway strategy'},
            {type: 'string', name: 'putawayStrategyValue', label: 'Putaway strategy value'},
            {type: 'string', name: 'removalStrategy', label: 'Removal strategy'},
            {type: 'string', name: 'removalStrategyValue', label: 'Removal strategy value'},
            {type: 'boolean', name: 'negativeStock', label: 'Allow negative stock'},
            {type: 'decimal', name: 'lastCost', label: 'Last cost'},
            {type: 'string', name: 'warrantyTemplate', label: 'Warranty template'},
            {type: 'text', name: 'serviceNote', label: 'Service note'},
            {type: 'decimal', name: 'cost', label: 'Cost'},
            {type: 'string', name: 'valuationMethod', label: 'Valuation method'},
            {type: 'string', name: 'valuationMethodValue', label: 'Valuation method value'},
            {type: 'string', name: 'accountSetGroup', label: 'Account set group'},
            {type: 'boolean', name: 'useShippedGoodsAccount', label: 'Use shipped goods account'},
            {type: 'string', name: 'fixtureAssetType', label: 'Fixture asset type'},
            {type: 'string', name: 'fixtureAssetCategory', label: 'Fixture asset category'},
            {type: 'string', name: 'fixtureAssetClass', label: 'Fixture asset class'},
            {type: 'string', name: 'fixtureModel', label: 'Fixture model'},

            {type: 'string', name: 'categoryId', label: 'Category ID'},
            {type: 'string', name: 'groupIds', label: 'Group IDS'},
            {type: 'string', name: 'baseUnitId', label: 'Base unit ID'},
            {type: 'string', name: 'brandId', label: 'Brand ID'},
            {type: 'string', name: 'typeOfGoodsId', label: 'Type of goods ID'},
            {type: 'string', name: 'manufacturerId', label: 'Manufacturer ID'},
            {type: 'string', name: 'countryOfManufactureId', label: 'Country of manufacture ID'},
            {type: 'string', name: 'countryOfOriginId', label: 'Country of origin ID'},
            {type: 'string', name: 'containerTypeId', label: 'Container type ID'},
            {type: 'string', name: 'shippingUnitId', label: 'Shipping unit ID'},
            {type: 'string', name: 'salesUnitId', label: 'Sales unit ID'},
            {type: 'string', name: 'salesTaxId', label: 'Sales tax ID'},
            {type: 'string', name: 'salesAdditionalTaxIds', label: 'Sales additional tax IDS'},
            {type: 'string', name: 'ecommerceDeliveryOptionIds', label: 'E-commerce delivery option IDS'},
            {type: 'string', name: 'purchaseUnitId', label: 'Purchase unit ID'},
            {type: 'string', name: 'purchaseTaxId', label: 'Purchase tax ID'},
            {type: 'string', name: 'purchaseAdditionalTaxIds', label: 'Purchase additional tax IDS'},
            {type: 'string', name: 'stockUnitId', label: 'Stock unit ID'},
            {type: 'string', name: 'warrantyTemplateId', label: 'Warranty template ID'},
            {type: 'string', name: 'accountSetGroupId', label: 'Account set group ID'},
            {type: 'string', name: 'fixtureAssetTypeId', label: 'Fixture asset type ID'},
            {type: 'string', name: 'fixtureAssetCategoryId', label: 'Fixture asset category ID'},
            {type: 'string', name: 'fixtureAssetClassId', label: 'Fixture asset class ID'},
            {type: 'string', name: 'fixtureModelId', label: 'Fixture model ID'},
            {type: 'string', name: 'configurableProductId', label: 'Configurable product ID'},
            {type: 'string', name: 'attributeSetId', label: 'Attribute set ID'},
            {type: 'string', name: 'tagIds', label: 'Tag IDS'},

            {
                type: 'array',
                name: 'barcodes',
                label: 'Barcodes',
                fields: [
                    {type: 'string', name: 'productCode', label: 'Product code'},
                    {type: 'string', name: 'productDefinition', label: 'Product definition'},
                    {type: 'string', name: 'unitName', label: 'Unit name'},
                    {type: 'decimal', name: 'unitRatio', label: 'Unit ratio'},
                    {type: 'string', name: 'barcode', label: 'Barcode'},

                    {type: 'string', name: 'unitId', label: 'Unit ID'}
                ]
            },

            {
                type: 'array',
                name: 'units',
                label: 'Units',
                fields: [
                    {type: 'string', name: 'productCode', label: 'Product code'},
                    {type: 'string', name: 'productDefinition', label: 'Product definition'},
                    {type: 'string', name: 'baseUnit', label: 'Base unit'},
                    {type: 'decimal', name: 'quantity', label: 'Quantity'},
                    {type: 'string', name: 'unitName', label: 'Unit name'},
                    {type: 'decimal', name: 'correspondingQuantity', label: 'Corresponding Quantity'},
                    {type: 'string', name: 'correspondingUnitName', label: 'Corresponding Unit name'},
                    {type: 'decimal', name: 'unitRatio', label: 'Unit ratio'},
                    {type: 'string', name: 'unitId', label: 'Unit ID'}
                ]
            },

            {
                type: 'array',
                name: 'tags',
                label: 'Tags',
                fields: [
                    {type: 'string', name: 'productCode', label: 'Product code'},
                    {type: 'string', name: 'productDefinition', label: 'Product definition'},
                    {type: 'string', name: 'tagCode', label: 'Tag code'},
                    {type: 'string', name: 'tagName', label: 'Tag name'},
                    {type: 'string', name: 'tagDescription', label: 'Tag description'},

                    {type: 'string', name: 'tagId', label: 'Tag ID'}
                ]
            }
        ];

        const attributeSets = await app.collection('inventory.product-attribute-sets').find({
            $select: ['attributes.type', 'attributes.code', 'attributes.label']
        });
        for (const attributeSet of attributeSets) {
            for (const attribute of attributeSet.attributes ?? []) {
                const name = _.camelCase(`attribute_${attribute.code}`);

                if (fields.findIndex(field => field.name === name) === -1) {
                    fields.push({
                        type: 'string',
                        name,
                        label: attribute.label
                    });
                }
            }
        }

        const featureSets = await app.collection('inventory.product-feature-sets').find({
            $select: ['fields.fieldType', 'fields.code', 'fields.label']
        });
        for (const featureSet of featureSets) {
            for (const field of featureSet.fields ?? []) {
                const name = _.camelCase(`feature_${field.code}`);

                if (fields.findIndex(field => field.name === name) === -1) {
                    const column = {
                        type: 'string',
                        name,
                        label: field.label
                    };

                    if (field.fieldType === 'integer') {
                        column.type = 'integer';
                    } else if (field.fieldType === 'decimal' || field.fieldType === 'money') {
                        column.type = 'decimal';
                    } else if (field.fieldType === 'date') {
                        column.type = 'date';
                    } else if (field.fieldType === 'datetime') {
                        column.type = 'datetime';
                    } else if (field.fieldType === 'time') {
                        column.type = 'time';
                    } else if (field.fieldType === 'boolean') {
                        column.type = 'boolean';
                    }

                    fields.push(column);
                }
            }
        }

        const ais = await app.collection('kernel.additional-information').find({
            type: {$in: ['stockable-product', 'service-product']},
            $select: ['fields.fieldType', 'fields.code', 'fields.label']
        });
        for (const ai of ais) {
            for (const field of ai.fields || []) {
                const name = _.camelCase(`info_${field.code}`);

                if (fields.findIndex(field => field.name === name) === -1) {
                    const column = {
                        type: 'string',
                        name,
                        label: field.label
                    };

                    if (field.fieldType === 'integer') {
                        column.type = 'integer';
                    } else if (field.fieldType === 'decimal' || field.fieldType === 'money') {
                        column.type = 'decimal';
                    } else if (field.fieldType === 'date') {
                        column.type = 'date';
                    } else if (field.fieldType === 'datetime') {
                        column.type = 'datetime';
                    } else if (field.fieldType === 'time') {
                        column.type = 'time';
                    } else if (field.fieldType === 'boolean') {
                        column.type = 'boolean';
                    }

                    fields.push(column);
                }
            }
        }

        return fields;
    },
    async bulkDocumentExtra(app, schema, documents) {
        let countriesMap = {};
        let countryIds = [];
        let unitsMap = {};
        let unitIds = [];
        let taxesMap = {};
        let taxIds = [];
        let groupsMap = {};
        let groupIds = [];
        let brandsMap = {};
        let brandIds = [];
        let typeOfGoodsMap = {};
        let typeOfGoodsIds = [];
        let manufacturersMap = {};
        let manufacturerIds = [];
        let containerTypesMap = {};
        let containerTypeIds = [];
        let warrantyTemplatesMap = {};
        let warrantyTemplateIds = [];
        let accountSetGroupsMap = {};
        let accountSetGroupIds = [];
        let fixtureAssetTypesMap = {};
        let fixtureAssetTypeIds = [];
        let fixtureAssetCategoriesMap = {};
        let fixtureAssetCategoryIds = [];
        let fixtureAssetClassesMap = {};
        let fixtureAssetClassIds = [];
        let fixtureModelsMap = {};
        let fixtureModelIds = [];
        let tagsMap = {};
        let tagIds = [];
        for (const document of documents || []) {


            if (!!document.baseUnitId) unitIds.push(document.baseUnitId);
            if (!!document.shippingUnitId) unitIds.push(document.shippingUnitId);
            if (!!document.salesUnitId) unitIds.push(document.salesUnitId);
            if (!!document.purchaseUnitId) unitIds.push(document.purchaseUnitId);
            if (!!document.stockUnitId) unitIds.push(document.stockUnitId);
            if (!!document.brandId) brandIds.push(document.brandId);
            if (!!document.typeOfGoodsId) typeOfGoodsIds.push(document.typeOfGoodsId);
            if (!!document.manufacturerId) manufacturerIds.push(document.manufacturerId);
            if (!!document.countryOfManufactureId) countryIds.push(document.countryOfManufactureId);
            if (!!document.countryOfOrigin) countryIds.push(document.countryOfOrigin);
            if (!!document.salesTaxId) taxIds.push(document.salesTaxId);
            if (!!document.purchaseTaxId) taxIds.push(document.purchaseTaxId);
            if (!!document.containerTypeId) containerTypeIds.push(document.containerTypeId);
            if (!!document.warrantyTemplateId) warrantyTemplateIds.push(document.warrantyTemplateId);
            if (!!document.accountSetGroupId) accountSetGroupIds.push(document.accountSetGroupId);
            if (!!document.fixtureAssetTypeId) fixtureAssetTypeIds.push(document.fixtureAssetTypeId);
            if (!!document.fixtureAssetCategoryId) fixtureAssetCategoryIds.push(document.fixtureAssetCategoryId);
            if (!!document.fixtureAssetClassId) fixtureAssetClassIds.push(document.fixtureAssetClassId);
            if (!!document.fixtureModelId) fixtureModelIds.push(document.fixtureModelId);

            if (Array.isArray(document.barcodes)) {
                unitIds.push(...document.barcodes.map(barcode => barcode.unitId));
            }
            if (Array.isArray(document.groupIds)) {
                groupIds.push(...document.groupIds);
            }
            if (Array.isArray(document.salesAdditionalTaxes)) {
                taxIds.push(...document.salesAdditionalTaxes);
            }
            if (Array.isArray(document.purchaseAdditionalTaxes)) {
                taxIds.push(...document.purchaseAdditionalTaxes);
            }
            if (Array.isArray(document.tagIds)) {
                tagIds.push(...document.tagIds);
            }

            for (const unitId of Object.keys(document.unitRatios ?? {})) {
                unitIds.push(unitId);
            }
        }
        countryIds = _.uniq(countryIds);
        unitIds = _.uniq(unitIds);
        taxIds = _.uniq(taxIds);
        groupIds = _.uniq(groupIds);
        brandIds = _.uniq(brandIds);
        typeOfGoodsIds = _.uniq(typeOfGoodsIds);
        manufacturerIds = _.uniq(manufacturerIds);
        containerTypeIds = _.uniq(containerTypeIds);
        warrantyTemplateIds = _.uniq(warrantyTemplateIds);
        accountSetGroupIds = _.uniq(accountSetGroupIds);
        fixtureAssetTypeIds = _.uniq(fixtureAssetTypeIds);
        fixtureAssetCategoryIds = _.uniq(fixtureAssetCategoryIds);
        fixtureAssetClassIds = _.uniq(fixtureAssetClassIds);
        tagIds = _.uniq(tagIds);
        if (countryIds.length > 0) {
            const countries = await app.collection('kernel.countries').find({
                _id: {$in: countryIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const country of countries) {
                countriesMap[country._id] = country;
            }
        }
        if (unitIds.length > 0) {
            const units = await app.collection('kernel.units').find({
                _id: {$in: unitIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const unit of units) {
                unitsMap[unit._id] = unit;
            }
        }
        if (taxIds.length > 0) {
            const taxes = await app.collection('kernel.taxes').find({
                _id: {$in: taxIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const tax of taxes) {
                taxesMap[tax._id] = tax;
            }
        }
        if (groupIds.length > 0) {
            const groups = await app.collection('inventory.product-groups').find({
                _id: {$in: groupIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const group of groups) {
                groupsMap[group._id] = group;
            }
        }
        if (brandIds.length > 0) {
            const brands = await app.collection('inventory.product-brands').find({
                _id: {$in: brandIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const brand of brands) {
                brandsMap[brand._id] = brand;
            }
        }
        if (typeOfGoodsIds.length > 0) {
            const typeOfGoods = await app.collection('inventory.types-of-goods').find({
                _id: {$in: typeOfGoodsIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const typeOfGood of typeOfGoods) {
                typeOfGoodsMap[typeOfGood._id] = typeOfGood;
            }
        }
        if (manufacturerIds.length > 0) {
            const manufacturers = await app.collection('kernel.partners').find({
                _id: {$in: manufacturerIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const manufacturer of manufacturers) {
                manufacturersMap[manufacturer._id] = manufacturer;
            }
        }
        if (containerTypeIds.length > 0) {
            const containerTypes = await app.collection('logistics.container-types').find({
                _id: {$in: containerTypeIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const containerType of containerTypes) {
                containerTypesMap[containerType._id] = containerType;
            }
        }
        if (warrantyTemplateIds.length > 0) {
            const warrantyTemplates = await app.collection('service.warranty-templates').find({
                _id: {$in: warrantyTemplateIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const warrantyTemplate of warrantyTemplates) {
                warrantyTemplatesMap[warrantyTemplate._id] = warrantyTemplate;
            }
        }
        if (accountSetGroupIds.length > 0) {
            const accountSetGroups = await app.collection('kernel.account-set-groups').find({
                _id: {$in: accountSetGroupIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const accountSetGroup of accountSetGroups) {
                accountSetGroupsMap[accountSetGroup._id] = accountSetGroup;
            }
        }
        if (fixtureAssetTypeIds.length > 0) {
            const fixtureAssetTypes = await app.collection('am.asset-types').find({
                _id: {$in: fixtureAssetTypeIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const fixtureAssetType of fixtureAssetTypes) {
                fixtureAssetTypesMap[fixtureAssetType._id] = fixtureAssetType;
            }
        }
        if (fixtureAssetCategoryIds.length > 0) {
            const fixtureAssetCategories = await app.collection('am.asset-categories').find({
                _id: {$in: fixtureAssetCategoryIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const fixtureAssetCategory of fixtureAssetCategories) {
                fixtureAssetCategoriesMap[fixtureAssetCategory._id] = fixtureAssetCategory;
            }
        }
        if (fixtureAssetClassIds.length > 0) {
            const fixtureAssetClasses = await app.collection('am.asset-classes').find({
                _id: {$in: fixtureAssetClassIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const fixtureAssetClass of fixtureAssetClasses) {
                fixtureAssetClassesMap[fixtureAssetClass._id] = fixtureAssetClass;
            }
        }
        if (fixtureModelIds.length > 0) {
            const fixtureModels = await app.collection('am.models').find({
                _id: {$in: fixtureModelIds},
                $select: ['name'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const fixtureModel of fixtureModels) {
                fixtureModelsMap[fixtureModel._id] = fixtureModel;
            }
        }
        if (tagIds.length > 0) {
            const tags = await app.collection('inventory.product-tags').find({
                _id: {$in: tagIds},
                $select: ['_id', 'code', 'name', 'description'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });
            for (const tag of tags) {
                tagsMap[tag._id] = tag;
            }
        }

        const deliveryOptionsMap = {};
        const stores = await app.collection('ecommerce.stores').find({
            $select: ['name', 'deliveryOptions'],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
        for (const store of stores) {
            for (const option of store.deliveryOptions ?? []) {
                deliveryOptionsMap[option.id] = {
                    id: option.id,
                    name: `${store.name} - ${option.name}`
                };
            }
        }

        return {
            countriesMap,
            unitsMap,
            taxesMap,
            groupsMap,
            brandsMap,
            typeOfGoodsMap,
            manufacturersMap,
            containerTypesMap,
            deliveryOptionsMap,
            warrantyTemplatesMap,
            accountSetGroupsMap,
            fixtureAssetTypesMap,
            fixtureAssetCategoriesMap,
            fixtureAssetClassesMap,
            fixtureModelsMap,
            tagsMap
        };
    },
    async data(
        app,
        schema,
        document,
        {
            countriesMap,
            unitsMap,
            taxesMap,
            groupsMap,
            brandsMap,
            typeOfGoodsMap,
            manufacturersMap,
            containerTypesMap,
            deliveryOptionsMap,
            warrantyTemplatesMap,
            accountSetGroupsMap,
            fixtureAssetTypesMap,
            fixtureAssetCategoriesMap,
            fixtureAssetClassesMap,
            fixtureModelsMap,
            tagsMap
        }
    ) {
        const data = {...document};

        const typeOptions = [
            {value: 'stockable', label: 'Stockable product'},
            {value: 'service', label: 'Service product'}
        ];
        const typeLabel = (typeOptions.find(option => option.value === document.type) || {}).label || '';
        if (!!typeLabel) {
            data.type = app.translate(typeLabel);
        }
        data.typeValue = document.type;
        data.groups = [];
        for (const groupId of document.groupIds) {
            data.groups.push((groupsMap[groupId] ?? {}).name ?? '');
        }
        data.groups = data.groups.join(', ');
        data.baseUnit = app.translate((unitsMap[document.baseUnitId] ?? {}).name ?? '');
        if (!!document.brandId) {
            data.brand = (brandsMap[document.brandId] ?? {}).name ?? '';
        }
        if (!!document.typeOfGoodsId) {
            data.typeOfGoods = (typeOfGoodsMap[document.typeOfGoodsId] ?? {}).name ?? '';
        }
        if (!!document.manufacturerId) {
            data.manufacturer = (manufacturersMap[document.manufacturerId] ?? {}).name ?? '';
        }
        if (!!document.countryOfManufactureId) {
            data.countryOfManufacture = (countriesMap[document.countryOfManufactureId] ?? {}).name ?? '';
        }
        if (!!document.countryOfOriginId) {
            data.countryOfOrigin = (countriesMap[document.countryOfOriginId] ?? {}).name ?? '';
        }
        if (!!document.containerTypeId) {
            data.containerType = (containerTypesMap[document.containerTypeId] ?? {}).name ?? '';
        }
        if (!!document.shippingUnitId) {
            data.shippingUnit = (unitsMap[document.shippingUnitId] ?? {}).name ?? '';
        }
        if (!!document.salesUnitId) {
            data.salesUnit = app.translate((unitsMap[document.salesUnitId] ?? {}).name ?? '');
        }
        if (!!document.salesTaxId) {
            data.salesTax = (taxesMap[document.salesTaxId] ?? {}).name ?? '';
        }
        data.salesAdditionalTaxes = [];
        for (const salesAdditionalTaxId of document.salesAdditionalTaxIds ?? []) {
            data.salesAdditionalTaxes.push((taxesMap[salesAdditionalTaxId] ?? {}).name ?? '');
        }
        data.salesAdditionalTaxes = data.salesAdditionalTaxes.join(', ');
        data.ecommerceDeliveryOptions = [];
        for (const ecommerceDeliveryOptionId of document.ecommerceDeliveryOptionIds ?? []) {
            if (!!deliveryOptionsMap[ecommerceDeliveryOptionId]) {
                data.ecommerceDeliveryOptions.push(deliveryOptionsMap[ecommerceDeliveryOptionId].name);
            }
        }
        data.ecommerceDeliveryOptions = data.ecommerceDeliveryOptions.join(', ');
        if (!!document.purchaseUnitId) {
            data.purchaseUnit = app.translate((unitsMap[document.purchaseUnitId] ?? {}).name ?? '');
        }
        if (!!document.purchaseTaxId) {
            data.purchaseTax = (taxesMap[document.purchaseTaxId] ?? {}).name ?? '';
        }
        data.purchaseAdditionalTaxes = [];
        for (const purchaseAdditionalTaxId of document.purchaseAdditionalTaxIds ?? []) {
            data.purchaseAdditionalTaxes.push((taxesMap[purchaseAdditionalTaxId] ?? {}).name ?? '');
        }
        data.purchaseAdditionalTaxes = data.purchaseAdditionalTaxes.join(', ');
        if (!!document.stockUnitId) {
            data.stockUnit = app.translate((unitsMap[document.stockUnitId] ?? {}).name ?? '');
        }
        const trackingOptions = [
            {value: 'serial', label: 'By unique serial numbers'},
            {value: 'lot', label: 'By unique lot numbers'},
            {value: 'none', label: 'No tracking'}
        ];
        const trackingLabel = (trackingOptions.find(option => option.value === document.tracking) || {}).label || '';
        if (!!trackingLabel) {
            data.tracking = app.translate(trackingLabel);
        }
        data.trackingValue = document.tracking;
        const putawayStrategyOptions = [
            {value: 'manuel', label: 'Manuel'},
            {value: 'first-location', label: 'First location'},
            {value: 'last-location', label: 'Last location'},
            {value: 'default-location', label: 'Default location'}
        ];
        const putawayStrategyLabel =
            (putawayStrategyOptions.find(option => option.value === document.putawayStrategy) || {}).label || '';
        if (!!putawayStrategyLabel) {
            data.putawayStrategy = app.translate(putawayStrategyLabel);
        }
        data.putawayStrategyValue = document.putawayStrategy;
        const removalStrategyOptions = [
            {value: 'manuel', label: 'Manuel'},
            {value: 'fifo', label: 'FIFO'},
            {value: 'lifo', label: 'LIFO'},
            {value: 'fefo', label: 'FEFO'}
        ];
        const removalStrategyLabel =
            (removalStrategyOptions.find(option => option.value === document.removalStrategy) || {}).label || '';
        if (!!removalStrategyLabel) {
            data.removalStrategy = app.translate(removalStrategyLabel);
        }
        data.removalStrategyValue = document.removalStrategy;
        if (!!document.warrantyTemplateId) {
            data.warrantyTemplate = (warrantyTemplatesMap[document.warrantyTemplateId] ?? {}).name ?? '';
        }
        const valuationMethodOptions = [
            {value: 'standard', label: 'Standard'},
            {value: 'average', label: 'Average cost'},
            {value: 'fifo', label: 'FIFO'}
        ];
        const valuationMethodLabel =
            (valuationMethodOptions.find(option => option.value === document.valuationMethod) || {}).label || '';
        if (!!valuationMethodLabel) {
            data.valuationMethod = app.translate(valuationMethodLabel);
        }
        data.valuationMethodValue = document.valuationMethod;
        if (!!document.accountSetGroupId) {
            data.accountSetGroup = (accountSetGroupsMap[document.accountSetGroupId] ?? {}).name ?? '';
        }
        if (!!document.fixtureAssetTypeId) {
            data.fixtureAssetType = (fixtureAssetTypesMap[document.fixtureAssetTypeId] ?? {}).name ?? '';
        }
        if (!!document.fixtureAssetCategoryId) {
            data.fixtureAssetCategory = (fixtureAssetCategoriesMap[document.fixtureAssetCategoryId] ?? {}).name ?? '';
        }
        if (!!document.fixtureAssetClassId) {
            data.fixtureAssetClass = (fixtureAssetClassesMap[document.fixtureAssetClassId] ?? {}).name ?? '';
        }
        if (!!document.fixtureModelId) {
            data.fixtureModel = (fixtureModelsMap[document.fixtureModelId] ?? {}).name ?? '';
        }
        data.groupIds = (data.groupIds ?? []).join(', ');
        data.salesAdditionalTaxIds = (data.salesAdditionalTaxIds ?? []).join(', ');
        data.purchaseAdditionalTaxIds = (data.purchaseAdditionalTaxIds ?? []).join(', ');
        data.ecommerceDeliveryOptionIds = (data.ecommerceDeliveryOptionIds ?? []).join(', ');

        for (const key of Object.keys(document.attributes ?? {})) {
            data[_.camelCase(`attribute_${key}`)] = document.attributes[key] ?? '';
        }

        for (const key of Object.keys(document.features ?? {})) {
            data[_.camelCase(`feature_${key}`)] = document.features[key] ?? '';
        }

        for (const key of Object.keys(document.additionalInformation ?? {})) {
            data[_.camelCase(`info_${key}`)] = document.additionalInformation[key] ?? '';
        }

        data.barcodes = [];
        for (const barcode of document.barcodes ?? []) {
            const unit = unitsMap[barcode.unitId];
            const ratio = (document.unitRatios ?? {})[barcode.unitId] ?? 1;
            const b = {};

            b.productId = document._id;
            b.productCode = document.code;
            b.productDefinition = document.definition;
            b.unitId = barcode.unitId;
            b.unitName = app.translate((unit ?? {}).name ?? '');
            b.unitRatio = ratio;
            b.barcode = barcode.barcode;

            data.barcodes.push(b);
        }

        data.units = [];
        const unitConversions = document.unitConversions ?? [];

        for (const conv of unitConversions) {
            const ratio = (document.unitRatios ?? {})[conv.toUnitId] ?? 1;
            const u = {};
            u.productId = document._id;
            u.productCode = document.code;
            u.productDefinition = document.definition;
            u.unitId = conv.toUnitId;
            u.unitName = app.translate((unitsMap[conv.fromUnitId] ?? {}).name ?? '');
            u.correspondingUnitName = app.translate((unitsMap[conv.toUnitId] ?? {}).name ?? '');
            u.quantity = conv.fromQty;
            u.correspondingQuantity = conv.toQty;
            u.unitRatio = ratio;
            u.baseUnit = app.translate((unitsMap[document.baseUnitId] ?? {}).name ?? '');

            data.units.push(u);
        }

        data.tags = [];
        for (const tagId of document.tagIds ?? []) {
            const tag = tagsMap[tagId];
            if (!tag) continue;

            const t = {};

            t.productId = document._id;
            t.productCode = document.code;
            t.productDefinition = document.definition;
            t.tagId = tag._id;
            t.tagCode = tag.code;
            t.tagName = tag.name;
            t.tagDescription = tag.description;

            data.tags.push(t);
        }

        data.content = document.content;

        return data;
    }
};
